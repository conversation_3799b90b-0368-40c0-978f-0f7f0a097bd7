package com.jysd.wechat.platform.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;

import java.io.File;

/**
 * 微信小程序服务接口
 */
public interface WechatMiniappService {

    /**
     * 登录凭证校验，通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程
     *
     * @param appId 小程序appId
     * @param jsCode 登录时获取的 code
     * @return 用户openid和session_key等信息
     */
    WxMaJscode2SessionResult jsCode2SessionInfo(String appId, String jsCode);

    /**
     * 解密用户信息
     *
     * @param appId 小程序appId
     * @param sessionKey 会话密钥
     * @param encryptedData 加密的用户数据
     * @param ivStr 加密算法的初始向量
     * @return 用户信息
     */
    WxMaUserInfo getUserInfo(String appId, String sessionKey, String encryptedData, String ivStr);

    /**
     * 解密手机号信息
     *
     * @param appId 小程序appId
     * @param sessionKey 会话密钥
     * @param encryptedData 加密的手机号数据
     * @param ivStr 加密算法的初始向量
     * @return 手机号信息
     */
    WxMaPhoneNumberInfo getPhoneNoInfo(String appId, String sessionKey, String encryptedData, String ivStr);

    /**
     * 获取小程序码（适用于需要的码数量较少的业务场景）
     *
     * @param appId 小程序appId
     * @param path 扫码进入的小程序页面路径
     * @param width 二维码的宽度，单位 px，最小 280px，最大 1280px
     * @return 小程序码文件
     */
    File createWxaCode(String appId, String path, int width);

    /**
     * 获取小程序码（适用于需要的码数量极多的业务场景）
     *
     * @param appId 小程序appId
     * @param scene 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
     * @param page 页面 page，例如 pages/index/index，根路径前不要填加 /，不能携带参数
     * @param width 二维码的宽度，单位 px，最小 280px，最大 1280px
     * @return 小程序码文件
     */
    File createWxaCodeUnlimited(String appId, String scene, String page, int width);

    /**
     * 获取小程序二维码（适用于需要的码数量较少的业务场景）
     *
     * @param appId 小程序appId
     * @param path 扫码进入的小程序页面路径
     * @param width 二维码的宽度，单位 px，最小 280px，最大 1280px
     * @return 小程序二维码文件
     */
    File createQrCode(String appId, String path, int width);

    /**
     * 获取小程序访问令牌
     *
     * @param appId 小程序appId
     * @return access_token
     */
    String getAccessToken(String appId);
}
