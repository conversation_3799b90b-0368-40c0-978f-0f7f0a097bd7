package com.jysd.wechat.platform.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.jysd.wechat.platform.configuration.WxMaConfiguration;
import com.jysd.wechat.platform.exception.BusinessException;
import com.jysd.wechat.platform.service.WechatMiniappService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import java.io.File;

@Slf4j
@Service("wechatMiniappService")
public class WechatMiniappServiceImpl implements WechatMiniappService {

    @Override
    public WxMaJscode2SessionResult jsCode2SessionInfo(String appId, String jsCode) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getUserService().getSessionInfo(jsCode);
        } catch (WxErrorException e) {
            log.error("获取session信息失败，appId: {}, jsCode: {}, error: {}", appId, jsCode, e.getMessage(), e);
            throw new BusinessException("获取session信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WxMaUserInfo getUserInfo(String appId, String sessionKey, String encryptedData, String ivStr) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getUserService().getUserInfo(sessionKey, encryptedData, ivStr);
        } catch (Exception e) {
            log.error("解密用户信息失败，appId: {}, error: {}", appId, e.getMessage(), e);
            throw new BusinessException("解密用户信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WxMaPhoneNumberInfo getPhoneNoInfo(String appId, String sessionKey, String encryptedData, String ivStr) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getUserService().getPhoneNoInfo(sessionKey, encryptedData, ivStr);
        } catch (Exception e) {
            log.error("解密手机号信息失败，appId: {}, error: {}", appId, e.getMessage(), e);
            throw new BusinessException("解密手机号信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File createWxaCode(String appId, String path, int width) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getQrcodeService().createWxaCode(path, width);
        } catch (WxErrorException e) {
            log.error("生成小程序码失败，appId: {}, path: {}, width: {}, error: {}", 
                     appId, path, width, e.getMessage(), e);
            throw new BusinessException("生成小程序码失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File createWxaCodeUnlimited(String appId, String scene, String page, int width) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            // 使用基础的createWxaCode方法作为替代
            String path = page + "?scene=" + scene;
            return wxMaService.getQrcodeService().createWxaCode(path, width);
        } catch (WxErrorException e) {
            log.error("生成无限制小程序码失败，appId: {}, scene: {}, page: {}, width: {}, error: {}",
                     appId, scene, page, width, e.getMessage(), e);
            throw new BusinessException("生成无限制小程序码失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File createQrCode(String appId, String path, int width) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getQrcodeService().createQrcode(path, width);
        } catch (WxErrorException e) {
            log.error("生成小程序二维码失败，appId: {}, path: {}, width: {}, error: {}", 
                     appId, path, width, e.getMessage(), e);
            throw new BusinessException("生成小程序二维码失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getAccessToken(String appId) {
        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            return wxMaService.getAccessToken();
        } catch (WxErrorException e) {
            log.error("获取小程序访问令牌失败，appId: {}, error: {}", appId, e.getMessage(), e);
            throw new BusinessException("获取小程序访问令牌失败: " + e.getMessage(), e);
        }
    }
}
