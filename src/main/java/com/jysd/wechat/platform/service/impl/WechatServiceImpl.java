package com.jysd.wechat.platform.service.impl;

import com.jysd.wechat.platform.exception.BusinessException;
import com.jysd.wechat.platform.service.WechatService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Service;

@Slf4j
@AllArgsConstructor
@Service("wechatService")
public class WechatServiceImpl implements WechatService {

    private final WxMpService wxMpService;

    @Override
    public String getOpenId(String appId, String code) {
        try {
            WxOAuth2AccessToken auth2AccessToken = wxMpService.switchoverTo(appId).getOAuth2Service().getAccessToken(code);
            if (auth2AccessToken != null) {
                return auth2AccessToken.getOpenId();
            }
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }

        throw new BusinessException("无法获取到openId！请确认code是否正确！");
    }

    @Override
    public WxJsapiSignature getJsApiSignature(String appId, String decodePath) {
        try {
            return wxMpService.switchoverTo(appId).createJsapiSignature(decodePath);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    @Override
    public String getAccessToken(String appId) {
        try {
            return wxMpService.switchoverTo(appId).getAccessToken();
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    @Override
    public WxMpUser getUserInfo(String appId, String openId) {
        WxMpUser wxMpUser;
        try {
            wxMpUser = wxMpService.switchoverTo(appId).getUserService().userInfo(openId);
        } catch (WxErrorException e) {
            wxMpUser = new WxMpUser();
            wxMpUser.setSubscribe(false);
            wxMpUser.setOpenId(openId);
        }
        return wxMpUser;
    }

    @Override
    public WxOAuth2AccessToken getOAuth2AccessToken(String appId, String code) {
        try {
            return wxMpService.switchoverTo(appId).getOAuth2Service().getAccessToken(code);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }
}
