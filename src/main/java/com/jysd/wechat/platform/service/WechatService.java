package com.jysd.wechat.platform.service;

import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

public interface WechatService {

    String getOpenId(String appId, String code);

    WxJsapiSignature getJsApiSignature(String appId, String decodePath);

    String getAccessToken(String appId);

    WxMpUser getUserInfo(String appId, String openId);

    WxOAuth2AccessToken getOAuth2AccessToken(String appId, String code);
}
