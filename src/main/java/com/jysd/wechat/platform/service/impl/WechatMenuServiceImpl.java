package com.jysd.wechat.platform.service.impl;

import com.jysd.wechat.platform.exception.BusinessException;
import com.jysd.wechat.platform.service.WechatMenuService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;

@Slf4j
@AllArgsConstructor
@Service("wechatMenuService")
public class WechatMenuServiceImpl implements WechatMenuService {

    private final WxMpService wxMpService;

    @Override
    public void create(String appId, WxMenu menu) {
        try {
            this.wxMpService.switchoverTo(appId).getMenuService().menuCreate(menu);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }
}
