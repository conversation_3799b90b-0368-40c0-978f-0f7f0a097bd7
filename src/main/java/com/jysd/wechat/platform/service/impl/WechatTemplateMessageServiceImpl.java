package com.jysd.wechat.platform.service.impl;

import com.jysd.wechat.platform.common.MessageParam;
import com.jysd.wechat.platform.exception.BusinessException;
import com.jysd.wechat.platform.service.WechatTemplateMessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpTemplateMsgServiceImpl;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Service("wechatTemplateMessageService")
public class WechatTemplateMessageServiceImpl implements WechatTemplateMessageService {

    private final WxMpService wxMpService;

    @Override
    public void sendTemplateMessage(String appId, MessageParam messageParam) {
        try {
            WxMpTemplateMsgServiceImpl wxMpTemplateMsgService = new WxMpTemplateMsgServiceImpl(wxMpService.switchoverTo(appId));
            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
            wxMpTemplateMessage.setToUser(messageParam.getOpenId());
            wxMpTemplateMessage.setTemplateId(messageParam.getTemplateId());
            wxMpTemplateMessage.setUrl(messageParam.getUrl());

            List<WxMpTemplateData> wxMpTemplateDatas = new ArrayList<>();
            wxMpTemplateDatas.add(new WxMpTemplateData("first", messageParam.getFirst(), messageParam.getColorFirst()));
            wxMpTemplateDatas.add(new WxMpTemplateData("keyword1", messageParam.getKeyword1(), messageParam.getColor1()));
            wxMpTemplateDatas.add(new WxMpTemplateData("keyword2", messageParam.getKeyword2(), messageParam.getColor2()));
            wxMpTemplateDatas.add(new WxMpTemplateData("keyword3", messageParam.getKeyword3(), messageParam.getColor3()));
            wxMpTemplateDatas.add(new WxMpTemplateData("keyword4", messageParam.getKeyword4(), messageParam.getColor4()));
            wxMpTemplateDatas.add(new WxMpTemplateData("keyword5", messageParam.getKeyword5(), messageParam.getColor5()));
            wxMpTemplateDatas.add(new WxMpTemplateData("remark", messageParam.getRemark(), messageParam.getColorRemark()));
            wxMpTemplateMessage.setData(wxMpTemplateDatas);

            if (StringUtils.isNotEmpty(messageParam.getAppid())) {
                WxMpTemplateMessage.MiniProgram miniProgram = new WxMpTemplateMessage.MiniProgram();
                miniProgram.setAppid(messageParam.getAppid());
                miniProgram.setPagePath(messageParam.getPagePath());
                wxMpTemplateMessage.setMiniProgram(miniProgram);
            }

            wxMpTemplateMsgService.sendTemplateMsg(wxMpTemplateMessage);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

}
