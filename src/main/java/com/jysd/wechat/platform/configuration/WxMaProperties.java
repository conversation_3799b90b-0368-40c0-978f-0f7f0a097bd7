package com.jysd.wechat.platform.configuration;

import lombok.Data;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.List;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Data
@ConfigurationProperties(prefix = "wx.miniapp")
public class WxMaProperties {

    private List<Config> configs;

    @Data
    public static class Config {
        /**
         * 设置微信小程序的appid
         */
        private String appid;

        /**
         * 设置微信小程序的Secret
         */
        private String secret;

        /**
         * 设置微信小程序消息服务器配置的token
         */
        private String token;

        /**
         * 设置微信小程序消息服务器配置的EncodingAESKey
         */
        private String aesKey;

        /**
         * 消息格式，XML或者JSON
         */
        private String msgDataFormat;
    }

    /**
     * 存储策略
     */
    private final ConfigStorage configStorage = new ConfigStorage();

    @Data
    public static class ConfigStorage {

        /**
         * 指定key前缀.
         */
        private String keyPrefix = "new_comers_miniapp_";

        /**
         * redis连接配置.
         */
        @NestedConfigurationProperty
        private final RedisProperties redis = new RedisProperties();


        /**
         * http 请求重试间隔
         * <pre>
         *   {@link cn.binarywang.wx.miniapp.api.impl.BaseWxMaServiceImpl#setRetrySleepMillis(int)}
         * </pre>
         */
        private int retrySleepMillis = 1000;
        /**
         * http 请求最大重试次数
         * <pre>
         *   {@link cn.binarywang.wx.miniapp.api.impl.BaseWxMaServiceImpl#setMaxRetryTimes(int)}
         * </pre>
         */
        private int maxRetryTimes = 5;
    }
}
