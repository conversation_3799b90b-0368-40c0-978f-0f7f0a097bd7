package com.jysd.wechat.platform.configuration;

import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;
import com.github.dozermapper.core.loader.api.BeanMappingBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DozerConfiguration {

    @Bean
    public Mapper beanMapper() {
        return DozerBeanMapperBuilder.create()
                .withMappingBuilders(beanMappingBuilder())
                .build();
    }

    @Bean
    public BeanMappingBuilder beanMappingBuilder() {
        return new BeanMappingBuilder() {
            @Override
            protected void configure() {
//                mapping(CreateRoleDTO.class,RoleDO.class)
//                        .fields("address","addr");
            }
        };
    }

}
