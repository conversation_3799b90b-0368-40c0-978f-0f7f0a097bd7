package com.jysd.wechat.platform.controller;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.jysd.wechat.platform.common.Response;
import com.jysd.wechat.platform.service.WechatMiniappService;
import com.jysd.wechat.platform.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/wechat/miniapp/{appId}")
@Api(tags = "WechatMiniappController", description = "微信小程序相关操作")
public class WechatMiniappController {

    private final WechatMiniappService wechatMiniappService;

    @ApiOperation("小程序登录")
    @GetMapping("/login")
    public Response<WxMaJscode2SessionResult> login(
            @PathVariable String appId,
            @ApiParam("小程序登录时获取的code") @RequestParam String jsCode) {
        WxMaJscode2SessionResult sessionInfo = wechatMiniappService.jsCode2SessionInfo(appId, jsCode);
        return ResponseUtils.success(sessionInfo);
    }

    @ApiOperation("获取用户信息")
    @PostMapping("/user-info")
    public Response<WxMaUserInfo> getUserInfo(
            @PathVariable String appId,
            @ApiParam("会话密钥") @RequestParam String sessionKey,
            @ApiParam("加密的用户数据") @RequestParam String encryptedData,
            @ApiParam("加密算法的初始向量") @RequestParam String iv) {
        WxMaUserInfo userInfo = wechatMiniappService.getUserInfo(appId, sessionKey, encryptedData, iv);
        return ResponseUtils.success(userInfo);
    }

    @ApiOperation("获取手机号")
    @PostMapping("/phone-number")
    public Response<WxMaPhoneNumberInfo> getPhoneNumber(
            @PathVariable String appId,
            @ApiParam("会话密钥") @RequestParam String sessionKey,
            @ApiParam("加密的手机号数据") @RequestParam String encryptedData,
            @ApiParam("加密算法的初始向量") @RequestParam String iv) {
        WxMaPhoneNumberInfo phoneInfo = wechatMiniappService.getPhoneNoInfo(appId, sessionKey, encryptedData, iv);
        return ResponseUtils.success(phoneInfo);
    }

    @ApiOperation("生成小程序码")
    @GetMapping("/qrcode")
    public ResponseEntity<FileSystemResource> createWxaCode(
            @PathVariable String appId,
            @ApiParam("小程序页面路径") @RequestParam String path,
            @ApiParam("二维码宽度，默认430") @RequestParam(defaultValue = "430") int width) {
        File qrCodeFile = wechatMiniappService.createWxaCode(appId, path, width);
        
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=miniapp_qrcode.jpg");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(qrCodeFile));
    }

    @ApiOperation("生成无限制小程序码")
    @GetMapping("/qrcode-unlimited")
    public ResponseEntity<FileSystemResource> createWxaCodeUnlimited(
            @PathVariable String appId,
            @ApiParam("场景值，最大32个可见字符") @RequestParam String scene,
            @ApiParam("页面路径，例如 pages/index/index") @RequestParam(required = false) String page,
            @ApiParam("二维码宽度，默认430") @RequestParam(defaultValue = "430") int width) {
        File qrCodeFile = wechatMiniappService.createWxaCodeUnlimited(appId, scene, page, width);
        
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=miniapp_qrcode_unlimited.jpg");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(qrCodeFile));
    }

    @ApiOperation("生成小程序二维码")
    @GetMapping("/qr-code")
    public ResponseEntity<FileSystemResource> createQrCode(
            @PathVariable String appId,
            @ApiParam("小程序页面路径") @RequestParam String path,
            @ApiParam("二维码宽度，默认430") @RequestParam(defaultValue = "430") int width) {
        File qrCodeFile = wechatMiniappService.createQrCode(appId, path, width);
        
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=miniapp_qr_code.jpg");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(qrCodeFile));
    }

    @ApiOperation("获取小程序访问令牌")
    @GetMapping("/access-token")
    public Response<String> getAccessToken(@PathVariable String appId) {
        String accessToken = wechatMiniappService.getAccessToken(appId);
        return ResponseUtils.success(accessToken);
    }
}
