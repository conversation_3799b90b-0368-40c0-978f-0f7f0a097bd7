package com.jysd.wechat.platform.controller;

import com.jysd.wechat.platform.common.Response;
import com.jysd.wechat.platform.service.WechatService;
import com.jysd.wechat.platform.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/wechat/{appId}")
@Api(tags = "WechatController", description = "微信相关通用操作")
public class WechatController {

    private final WechatService wechatService;

    @ApiOperation("获取微信用户的openid")
    @GetMapping(path = "/openid")
    public Response<String> getOpenId(@PathVariable String appId, @ApiParam("所需code") @RequestParam String code) {
        String getOpenId = wechatService.getOpenId(appId, code);
        return ResponseUtils.success(getOpenId);
    }

    @ApiOperation("获取access-token")
    @GetMapping(path = "/access-token")
    public Response<String> getAccessToken(@PathVariable String appId) {
        String accessToken = wechatService.getAccessToken(appId);
        return ResponseUtils.success(accessToken);
    }

    @ApiOperation("获取jsapi签名")
    @GetMapping(path = "/jsapi/signature")
    public Response<WxJsapiSignature> getJsApiSignature(@PathVariable String appId, @RequestParam String path) throws UnsupportedEncodingException {
        String decodePath = URLDecoder.decode(path, "utf-8");
        WxJsapiSignature jsApiSignature = wechatService.getJsApiSignature(appId, decodePath);
        return ResponseUtils.success(jsApiSignature);
    }

    @ApiOperation("根据openid获取微信用户信息")
    @GetMapping("/user-info")
    public Response<WxMpUser> getUserInfo(@PathVariable String appId, @RequestParam("openId") String openId) {
        WxMpUser wxMpUser = wechatService.getUserInfo(appId, openId);
        return ResponseUtils.success(wxMpUser);
    }

    @ApiOperation("根据code获取OAuth2AccessToken")
    @GetMapping("/oauth/access-token")
    public Response<WxOAuth2AccessToken> getOAuth2AccessToken(@PathVariable String appId, @RequestParam("code") String code) {
        WxOAuth2AccessToken wxOAuth2AccessToken = wechatService.getOAuth2AccessToken(appId, code);
        return ResponseUtils.success(wxOAuth2AccessToken);
    }
}
