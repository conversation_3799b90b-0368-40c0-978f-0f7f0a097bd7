package com.jysd.wechat.platform.controller;

import com.jysd.wechat.platform.common.MessageParam;
import com.jysd.wechat.platform.common.Response;
import com.jysd.wechat.platform.service.WechatTemplateMessageService;
import com.jysd.wechat.platform.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/wechat/template/message/{appId}")
@Api(tags = "WechatTemplateMessageController", description = "模板消息")
public class WechatTemplateMessageController {

    private final WechatTemplateMessageService wechatTemplateMessageService;

    @ApiOperation("发送微信模板消息")
    @PostMapping(path = "/sending", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Response<String> sendTemplateMessage(@PathVariable String appId, @RequestBody MessageParam messageParam) {
        wechatTemplateMessageService.sendTemplateMessage(appId, messageParam);
        return ResponseUtils.success();
    }

}
