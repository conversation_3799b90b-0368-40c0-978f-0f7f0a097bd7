package com.jysd.wechat.platform.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaMessage;
import cn.binarywang.wx.miniapp.message.WxMaMessageRouter;
import com.jysd.wechat.platform.configuration.WxMaConfiguration;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 微信小程序消息接收控制器
 * 用于接收微信服务器推送的消息和事件
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat/miniapp/portal/{appId}")
@Api(tags = "WechatMiniappPortalController", description = "微信小程序消息接收")
public class WechatMiniappPortalController {

    @ApiOperation("微信服务器验证")
    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable String appId,
                          @RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        log.info("接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature, timestamp, nonce, echostr);

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            if (wxMaService == null) {
                throw new IllegalArgumentException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
            }

            if (wxMaService.checkSignature(timestamp, nonce, signature)) {
                return echostr;
            }
        } catch (Exception e) {
            log.error("微信服务器验证失败", e);
            throw new IllegalArgumentException("微信服务器验证失败: " + e.getMessage());
        }

        return "非法请求";
    }

    @ApiOperation("接收微信服务器消息")
    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable String appId,
                       @RequestBody String requestBody,
                       @RequestParam("signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce,
                       @RequestParam(name = "encrypt_type", required = false) String encType,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature) {

        log.info("接收微信请求：[appId=[{}], signature=[{}], encType=[{}], msgSignature=[{}], requestBody=[\n{}\n] ",
                appId, signature, encType, msgSignature, requestBody);

        try {
            WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            if (wxMaService == null) {
                throw new IllegalArgumentException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
            }

            if (!wxMaService.checkSignature(timestamp, nonce, signature)) {
                throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
            }

            String out = null;
            if (encType == null) {
                // 明文传输的消息
                WxMaMessage inMessage = WxMaMessage.fromXml(requestBody);
                this.route(appId, inMessage);
                out = "success";
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                WxMaMessage inMessage = WxMaMessage.fromEncryptedXml(requestBody, wxMaService.getWxMaConfig(),
                        timestamp, nonce, msgSignature);
                log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
                this.route(appId, inMessage);
                out = "success";
            }

            log.debug("\n组装回复信息：{}", out);
            return out;
        } catch (Exception e) {
            log.error("处理微信消息时出现异常", e);
            return "error";
        }
    }

    /**
     * 路由消息到对应的处理器
     */
    private void route(String appId, WxMaMessage message) {
        try {
            WxMaMessageRouter messageRouter = WxMaConfiguration.getRouter(appId);
            if (messageRouter != null) {
                messageRouter.route(message);
            } else {
                log.warn("未找到对应appId=[{}]的消息路由器", appId);
            }
        } catch (Exception e) {
            log.error("路由消息时出现异常！", e);
        }
    }
}
