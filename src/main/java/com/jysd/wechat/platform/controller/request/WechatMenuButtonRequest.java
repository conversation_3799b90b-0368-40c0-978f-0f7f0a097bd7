package com.jysd.wechat.platform.controller.request;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class WechatMenuButtonRequest {

    private String type;
    private String name;
    private String key;
    private String url;
    private String mediaId;
    private String appId;
    private String pagePath;
    private List<WechatMenuButtonRequest> subButtons = new ArrayList<>();

}
