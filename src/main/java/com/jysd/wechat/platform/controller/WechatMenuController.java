package com.jysd.wechat.platform.controller;

import com.github.dozermapper.core.Mapper;
import com.jysd.wechat.platform.common.Response;
import com.jysd.wechat.platform.controller.request.WechatMenuCreateRequest;
import com.jysd.wechat.platform.service.WechatMenuService;
import com.jysd.wechat.platform.utils.ResponseUtils;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/api/wechat/menu/{appId}")
@Api(tags = "WechatMenuController", description = "公众号菜单")
public class WechatMenuController {

    private final WechatMenuService wechatMenuService;
    private final Mapper beanMapper;

    @PostMapping(consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Response<String> create(@PathVariable String appId, @RequestBody WechatMenuCreateRequest request) {
        WxMenu menu = beanMapper.map(request, WxMenu.class);
        wechatMenuService.create(appId, menu);
        return ResponseUtils.success();
    }

}
