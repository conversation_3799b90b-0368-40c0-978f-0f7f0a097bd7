package com.jysd.wechat.platform.utils;

import com.jysd.wechat.platform.common.Response;
import org.springframework.http.HttpStatus;

public class ResponseUtils {

    private static final String INTERFACE_INVOKED_MESSAGE_SUCCESS = "接口调用成功";
    private static final String INTERFACE_INVOKED_MESSAGE_FAILURE = "接口调用失败";

    public static final String EMPTY_STRING = "";

    /**
     * 接口调用成功时生成响应对象，并指定响应码为200
     *
     * @return Response
     */
    public static Response<String> success() {
        return success(HttpStatus.OK.value(), INTERFACE_INVOKED_MESSAGE_SUCCESS, EMPTY_STRING);
    }

    /**
     * 接口调用成功时生成响应对象，并指定响应码和响应数据对象
     *
     * @param code 指定的响应码，如200
     * @param t    指定的响应数据对象
     * @param <T>  响应对象类型
     * @return Response<T>
     */
    public static <T> Response<T> success(int code, T t) {
        return success(code, INTERFACE_INVOKED_MESSAGE_SUCCESS, t);
    }

    /**
     * 接口调用成功时生成响应对象，并指定响应码和消息提示信息
     *
     * @param code    指定的响应码，如200
     * @param message 消息提示信息，如“接口调用成功”
     * @return Response<String>
     */
    public static Response<String> success(int code, String message) {
        return success(code, message, EMPTY_STRING);
    }

    /**
     * 接口调用成功时生成响应对象，并指定响应码、消息提示信息和响应数据对象
     *
     * @param code    指定的响应码，如200
     * @param message 消息提示信息，如“接口调用成功”
     * @param t       指定的响应数据对象
     * @param <T>     响应对象类型
     * @return Response<T>
     */
    public static <T> Response<T> success(int code, String message, T t) {
        return new Response<>(code, message, t);
    }

    /**
     * 接口调用成功时生成响应对象，并指定响应码为200和响应数据对象
     *
     * @param t   指定的响应数据对象
     * @param <T> 响应对象类型
     * @return Response<T>
     */
    public static <T> Response<T> success(T t) {
        return success(HttpStatus.OK.value(), INTERFACE_INVOKED_MESSAGE_SUCCESS, t);
    }

    /**
     * 接口调用失败时生成响应对象，并指定响应码为500和响应数据对
     *
     * @return Response
     */
    public static Response<String> error() {
        return error(HttpStatus.INTERNAL_SERVER_ERROR.value(), INTERFACE_INVOKED_MESSAGE_FAILURE, EMPTY_STRING);
    }

    /**
     * 接口调用失败时生成响应对象，并指定响应码为500和响应消息提示信息
     *
     * @param message 消息提示信息，如“接口调用失败”
     * @return
     */
    public static Response<String> error(String message) {
        return error(HttpStatus.INTERNAL_SERVER_ERROR.value(), message, EMPTY_STRING);
    }

    /**
     * 接口调用失败时生成响应对象，并指定响应码和响应消息提示信息
     *
     * @param code    指定的响应码，如500
     * @param message 消息提示信息，如“接口调用失败”
     * @return Response
     */
    public static Response<String> error(int code, String message) {
        return error(code, message, EMPTY_STRING);
    }

    /**
     * 接口调用失败时生成响应对象，并指定响应码、消息提示信息和响应数据对象
     *
     * @param code    指定的响应码，如500
     * @param message 消息提示信息，如“接口调用失败”
     * @param t       指定的响应数据对象
     * @param <T>     响应对象类型
     * @return Response<T>
     */
    public static <T> Response<T> error(int code, String message, T t) {
        return new Response<>(code, message, t);
    }
}
