package com.jysd.wechat.platform.runner;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static me.chanjar.weixin.common.api.WxConsts.MenuButtonType;

@Slf4j
@Order
@AllArgsConstructor
@Component("menuCreateRunner")
public class MenuCreateRunner implements CommandLineRunner {

    private final WxMpService wxMpService;

    @Override
    public void run(String... args) throws Exception {

//        initFirstWxMenu();
//        initSecondeWxMenu();
    }

    private void initFirstWxMenu() throws WxErrorException {
        WxMenu wxMenu = new WxMenu();

        // “小助手”菜单
        WxMenuButton littleHelperButton = new WxMenuButton();
        littleHelperButton.setName("小助手111");

        // “访校预约指引”菜单
        WxMenuButton reserveGuideButton = new WxMenuButton();
        reserveGuideButton.setType(MenuButtonType.VIEW);
        reserveGuideButton.setName("操作指导111");
        reserveGuideButton.setUrl("https://mp.weixin.qq.com/mp/homepage?__biz=MzU5MDg1Mzc0NA==&hid=1&sn=b79fecc6300bfca349ae87ba4327aca8");
        littleHelperButton.getSubButtons().add(reserveGuideButton);

        wxMenu.getButtons().add(littleHelperButton);

        // “现场预约”菜单
        WxMenuButton siteReservationButton = new WxMenuButton();
        siteReservationButton.setType(MenuButtonType.VIEW);
        siteReservationButton.setName("现场预约111");
        siteReservationButton.setUrl("https://mp.weixin.qq.com/mp/homepage?__biz=MzU5MDg1Mzc0NA==&hid=1&sn=b79fecc6300bfca349ae87ba4327aca8");
        wxMenu.getButtons().add(siteReservationButton);

        wxMpService.switchoverTo("wx5523f4a493ff4b28").getMenuService().menuCreate(wxMenu);
    }

    private void initSecondeWxMenu() throws WxErrorException {
        WxMenu wxMenu = new WxMenu();

        // “小助手”菜单
        WxMenuButton littleHelperButton = new WxMenuButton();
        littleHelperButton.setName("小助手222");

        // “访校预约指引”菜单
        WxMenuButton reserveGuideButton = new WxMenuButton();
        reserveGuideButton.setType(MenuButtonType.VIEW);
        reserveGuideButton.setName("操作指导222");
        reserveGuideButton.setUrl("https://mp.weixin.qq.com/mp/homepage?__biz=MzU5MDg1Mzc0NA==&hid=1&sn=b79fecc6300bfca349ae87ba4327aca8");
        littleHelperButton.getSubButtons().add(reserveGuideButton);

        wxMenu.getButtons().add(littleHelperButton);

        // “现场预约”菜单
        WxMenuButton siteReservationButton = new WxMenuButton();
        siteReservationButton.setType(MenuButtonType.VIEW);
        siteReservationButton.setName("现场预约222");
        siteReservationButton.setUrl("https://mp.weixin.qq.com/mp/homepage?__biz=MzU5MDg1Mzc0NA==&hid=1&sn=b79fecc6300bfca349ae87ba4327aca8");
        wxMenu.getButtons().add(siteReservationButton);

        wxMpService.switchoverTo("wx3f168924c577a8f2").getMenuService().menuCreate(wxMenu);
    }
}
