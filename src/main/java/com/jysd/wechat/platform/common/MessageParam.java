package com.jysd.wechat.platform.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("模板消息参数")
public class MessageParam {
    @ApiModelProperty(value = "模板ID", required = true)
    private String templateId;
    @ApiModelProperty(value = "微信用户OpenID", required = true)
    private String openId;
    @ApiModelProperty("first参数")
    private String first;
    @ApiModelProperty("first参数的文字颜色")
    private String colorFirst;
    @ApiModelProperty("remark参数")
    private String remark;
    @ApiModelProperty("remark参数的文字颜色")
    private String colorRemark;
    @ApiModelProperty("keyword1参数")
    private String keyword1;
    @ApiModelProperty("keyword1参数的文字颜色")
    private String color1;
    @ApiModelProperty("keyword2参数")
    private String keyword2;
    @ApiModelProperty("keyword2参数的文字颜色")
    private String color2;
    @ApiModelProperty("keyword3参数")
    private String keyword3;
    @ApiModelProperty("keyword3参数的文字颜色")
    private String color3;
    @ApiModelProperty("keyword4参数")
    private String keyword4;
    @ApiModelProperty("keyword4参数的文字颜色")
    private String color4;
    @ApiModelProperty("keyword5参数")
    private String keyword5;
    @ApiModelProperty("keyword5参数的文字颜色")
    private String color5;

    @ApiModelProperty("详情url")
    private String url;

    @ApiModelProperty("需要跳转小程序的appid，如果不需跳转，不传即可")
    private String appid;
    @ApiModelProperty("需要跳转小程序的pagePath")
    private String pagePath;
}
