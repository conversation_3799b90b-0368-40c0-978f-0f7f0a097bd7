package com.jysd.wechat.platform.exception.handler;

import com.jysd.wechat.platform.common.Response;
import com.jysd.wechat.platform.exception.BusinessException;
import com.jysd.wechat.platform.utils.ResponseUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Response<String> handleException(BusinessException exception) {
        return ResponseUtils.error(exception.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Response<String> handleException(Exception exception) {
        return ResponseUtils.error(exception.getMessage());
    }

    @ExceptionHandler(Throwable.class)
    public Response<String> handleException(Throwable exception) {
        return ResponseUtils.error(exception.getMessage());
    }

}
