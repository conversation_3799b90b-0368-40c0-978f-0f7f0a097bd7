# 微信小程序配置示例
# 请将此配置添加到您的 application.yml 或 application.properties 文件中

wx:
  miniapp:
    configs:
      - appid: your_miniapp_appid_here          # 小程序AppID
        secret: your_miniapp_secret_here        # 小程序AppSecret
        token: your_miniapp_token_here          # 小程序Token（可选）
        aesKey: your_miniapp_aes_key_here       # 小程序AESKey（可选）
        msgDataFormat: JSON                     # 消息格式：JSON 或 XML
    configStorage:
      keyPrefix: wx_miniapp_                    # Redis key前缀
      retrySleepMillis: 1000                    # HTTP请求重试间隔（毫秒）
      maxRetryTimes: 5                          # HTTP请求最大重试次数

# 注意：
# 1. 请将 your_miniapp_appid_here 替换为您的小程序AppID
# 2. 请将 your_miniapp_secret_here 替换为您的小程序AppSecret
# 3. token 和 aesKey 是可选的，如果您的小程序需要接收消息推送，则需要配置
# 4. 支持多个小程序配置，只需在 configs 下添加更多配置项即可
