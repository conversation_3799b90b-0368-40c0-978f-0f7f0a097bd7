# 微信小程序集成说明

## 概述

本项目已成功集成微信小程序功能，支持小程序码生成、用户登录、用户信息解密等功能。

## 新增功能

### 1. 小程序码生成
- **普通小程序码**: 适用于需要的码数量较少的业务场景
- **无限制小程序码**: 适用于需要的码数量极多的业务场景  
- **小程序二维码**: 传统二维码格式

### 2. 用户管理
- **小程序登录**: 通过 wx.login 获取的 code 进行登录验证
- **用户信息解密**: 解密小程序端获取的用户信息
- **手机号解密**: 解密小程序端获取的手机号信息

### 3. 基础功能
- **访问令牌获取**: 获取小程序的 access_token

## 配置说明

### 1. 添加小程序配置

在您的 `application.yml` 或 `application.properties` 文件中添加以下配置：

```yaml
wx:
  miniapp:
    configs:
      - appid: your_miniapp_appid_here          # 小程序AppID
        secret: your_miniapp_secret_here        # 小程序AppSecret
        token: your_miniapp_token_here          # 小程序Token（可选）
        aesKey: your_miniapp_aes_key_here       # 小程序AESKey（可选）
        msgDataFormat: JSON                     # 消息格式：JSON 或 XML
    configStorage:
      keyPrefix: wx_miniapp_                    # Redis key前缀
      retrySleepMillis: 1000                    # HTTP请求重试间隔（毫秒）
      maxRetryTimes: 5                          # HTTP请求最大重试次数
```

### 2. 多小程序支持

支持配置多个小程序，只需在 `configs` 下添加更多配置项：

```yaml
wx:
  miniapp:
    configs:
      - appid: miniapp1_appid
        secret: miniapp1_secret
        # ... 其他配置
      - appid: miniapp2_appid
        secret: miniapp2_secret
        # ... 其他配置
```

## API 接口说明

### 基础路径
所有小程序相关接口的基础路径为：`/api/wechat/miniapp/{appId}`

### 接口列表

#### 1. 小程序登录
- **路径**: `GET /api/wechat/miniapp/{appId}/login`
- **参数**: 
  - `jsCode`: 小程序登录时获取的code
- **返回**: 包含 openid 和 session_key 的用户会话信息

#### 2. 获取用户信息
- **路径**: `POST /api/wechat/miniapp/{appId}/user-info`
- **参数**:
  - `sessionKey`: 会话密钥
  - `encryptedData`: 加密的用户数据
  - `iv`: 加密算法的初始向量
- **返回**: 解密后的用户信息

#### 3. 获取手机号
- **路径**: `POST /api/wechat/miniapp/{appId}/phone-number`
- **参数**:
  - `sessionKey`: 会话密钥
  - `encryptedData`: 加密的手机号数据
  - `iv`: 加密算法的初始向量
- **返回**: 解密后的手机号信息

#### 4. 生成小程序码
- **路径**: `GET /api/wechat/miniapp/{appId}/qrcode`
- **参数**:
  - `path`: 小程序页面路径
  - `width`: 二维码宽度（默认430）
- **返回**: 小程序码图片文件

#### 5. 生成无限制小程序码
- **路径**: `GET /api/wechat/miniapp/{appId}/qrcode-unlimited`
- **参数**:
  - `scene`: 场景值，最大32个可见字符
  - `page`: 页面路径（可选）
  - `width`: 二维码宽度（默认430）
- **返回**: 小程序码图片文件

#### 6. 生成小程序二维码
- **路径**: `GET /api/wechat/miniapp/{appId}/qr-code`
- **参数**:
  - `path`: 小程序页面路径
  - `width`: 二维码宽度（默认430）
- **返回**: 小程序二维码图片文件

#### 7. 获取访问令牌
- **路径**: `GET /api/wechat/miniapp/{appId}/access-token`
- **返回**: 小程序的 access_token

## 使用示例

### 1. 生成小程序码
```bash
curl -X GET "http://localhost:8080/api/wechat/miniapp/your_appid/qrcode?path=pages/index/index&width=430"
```

### 2. 小程序登录
```bash
curl -X GET "http://localhost:8080/api/wechat/miniapp/your_appid/login?jsCode=your_js_code"
```

### 3. 获取用户信息
```bash
curl -X POST "http://localhost:8080/api/wechat/miniapp/your_appid/user-info" \
  -d "sessionKey=your_session_key" \
  -d "encryptedData=encrypted_user_data" \
  -d "iv=initialization_vector"
```

## 注意事项

1. **配置要求**: 请确保正确配置小程序的 AppID 和 AppSecret
2. **Redis 依赖**: 项目使用 Redis 存储配置和缓存，请确保 Redis 服务正常运行
3. **权限验证**: 在生产环境中，建议添加适当的权限验证机制
4. **错误处理**: 所有接口都包含完善的错误处理和日志记录
5. **文件存储**: 生成的二维码文件为临时文件，建议根据业务需求进行持久化存储

## 依赖版本

- `weixin-java-miniapp`: 4.3.0
- `Spring Boot`: 2.3.12.RELEASE

## 技术支持

如有问题，请查看项目日志或联系开发团队。
